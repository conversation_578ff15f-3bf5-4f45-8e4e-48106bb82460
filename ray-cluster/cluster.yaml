apiVersion: ray.io/v1
kind: RayCluster
metadata:
  name: ray
  namespace: emr-536iaofa563eq7spmvzy-ray
spec:
  # If `enableInTreeAutoscaling` is true, the Autoscaler sidecar will be added to the Ray head pod.
  # Ray Autoscaler integration is Beta with KubeRay >= 0.3.0 and Ray >= 2.0.0.
  enableInTreeAutoscaling: true
  # `autoscalerOptions` is an OPTIONAL field specifying configuration overrides for the Ray Autoscaler.
  # The example configuration shown below represents the DEFAULT values.
  # (You may delete autoscalerOptions if the defaults are suitable.)
  autoscalerOptions:
    # `upscalingMode` is "Default" or "Aggressive."
    # Conservative: Upscaling is rate-limited; the number of pending worker pods is at most the size of the Ray cluster.
    # Default: Upscaling is not rate-limited.
    # Aggressive: An alias for Default; upscaling is not rate-limited.
    upscalingMode: Default
    # `idleTimeoutSeconds` is the number of seconds to wait before scaling down a worker pod which is not using Ray resources.
    idleTimeoutSeconds: 60
    # `image` optionally overrides the Autoscaler's container image. The Autoscaler uses the same image as the Ray container by default.
    ## image: "my-repo/my-custom-autoscaler-image:tag"
    # `imagePullPolicy` optionally overrides the Autoscaler container's default image pull policy (IfNotPresent).
    imagePullPolicy: Always
    # Optionally specify the Autoscaler container's securityContext.
    securityContext: {}
    env:
        - name: AUTOSCALER_MAX_CONCURRENT_LAUNCHES
          value: "10"
    envFrom: []
    # resources specifies optional resource request and limit overrides for the Autoscaler container.
    # The default Autoscaler resource limits and requests should be sufficient for production use-cases.
    # However, for large Ray clusters, we recommend monitoring container resource usage to determine if overriding the defaults is required.
    resources:
      limits:
        cpu: "500m"
        memory: "512Mi"
      requests:
        cpu: "500m"
        memory: "512Mi"
  # Ray head pod template
  headGroupSpec:
    # The `rayStartParams` are used to configure the `ray start` command.
    # See kuberay/docs/guidance/rayStartParams.md at master · ray-project/kuberay for the default settings of `rayStartParams` in KubeRay.
    # See https://docs.ray.io/en/latest/cluster/cli.html#ray-start for all available options in `rayStartParams`.
    rayStartParams:
      # Setting "num-cpus: 0" to avoid any Ray actors or tasks being scheduled on the Ray head Pod.
      num-cpus: "0"
      num-gpus: "0"
      # Use `resources` to optionally specify custom resource annotations for the Ray node.
      # The value of `resources` is a string-integer mapping.
      # Currently, `resources` must be provided in the specific format demonstrated below:
      # resources: '"{\"Custom1\": 1, \"Custom2\": 5}"'
    # Pod template
    template:
      spec:
        containers:
        # The Ray head container
        - name: ray-head
          image: registry.eqfleetcmder.com/eq-sim/eq-sil-beeos:ray_0815
          ports:
          - containerPort: 6379
            name: gcs
          - containerPort: 8265
            name: dashboard
          - containerPort: 10001
            name: client
          - containerPort: 8888
            name: jupyter
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","ray stop"]
          livenessProbe:
            exec:
              command:
              - bash
              - -c
              - wget -T 5 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success && wget -T 5 -q -O- http://localhost:8265/api/gcs_healthz | grep success
            failureThreshold: 120
            initialDelaySeconds: 30
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "2"
              memory: "16G"
            requests:
              cpu: "2"
              memory: "16G"
          volumeMounts:
          - mountPath: /data  # 手动更新的文件可以放这里
            name: ray-volume
        volumes:
        - name: ray-volume
          persistentVolumeClaim:
            claimName: ray-pvc
        imagePullSecrets:
        - name: docker-registry-secret
  workerGroupSpecs:
  # the Pod replicas in this group typed worker
  - replicas: 3
    minReplicas: 3
    maxReplicas: 3
    # logical group name, for this called small-group, also can be functional
    groupName: small-group
    # If worker pods need to be added, Ray Autoscaler can increment the `replicas`.
    # If worker pods need to be removed, Ray Autoscaler decrements the replicas, and populates the `workersToDelete` list.
    # KubeRay operator will remove Pods from the list until the desired number of replicas is satisfied.
    #scaleStrategy:
    #  workersToDelete:
    #  - raycluster-complete-worker-small-group-bdtwh
    #  - raycluster-complete-worker-small-group-hv457
    #  - raycluster-complete-worker-small-group-k8tj7
    rayStartParams: {}
      # resources: '"{\"Custom3\": 3, \"Custom4\": 6}"'
    # Pod template
    template:
      spec:
        affinity:
          podAntiAffinity:
            requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                - key: ray.io/cluster
                  operator: In
                  values:
                  - ray
                - key: ray.io/group
                  operator: In
                  values:
                  - small-group
              topologyKey: "kubernetes.io/hostname"
        containers:
        - name: ray-worker
          image: registry.eqfleetcmder.com/eq-sim/eq-sil-beeos:ray_0815
          lifecycle:
            preStop:
              exec:
                command: ["/bin/sh","-c","ray stop"]
          livenessProbe:
            exec:
              command:
              - bash
              - -c
              - wget -T 5 -q -O- http://localhost:52365/api/local_raylet_healthz | grep success
            failureThreshold: 120
            initialDelaySeconds: 30
            periodSeconds: 5
            successThreshold: 1
            timeoutSeconds: 5
          resources:
            limits:
              cpu: "4"
              memory: "8G"
            requests:
              cpu: "4"
              memory: "8G"
          volumeMounts:
          - mountPath: /data  # 手动更新的文件可以放这里
            name: ray-volume
        volumes:
        - name: ray-volume
          persistentVolumeClaim:
            claimName: ray-pvc
        imagePullSecrets:
        - name: docker-registry-secret
