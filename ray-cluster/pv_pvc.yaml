apiVersion: v1
kind: PersistentVolume
metadata:
  name: ray-pv # PV 名称。
spec:
  capacity:
    storage: 20Gi # 声明应用存储使用量。Kubernetes 必填字段，但此处设置的容量值不会限制应用实际使用量，实际容量仅跟 TOS 容量有关。
  accessModes:
  # 访问方式，ReadWriteMany：多点读写。
  - ReadWriteMany
  csi:
    driver: tos.csi.volcengine.com # 驱动类型，本示例为 tos.csi.volcengine.com ，表示火山引擎 csi-tos 插件。
    nodePublishSecretRef:
      name: data-get-tos-key
      namespace: airflow
    nodeStageSecretRef:
      # 从 csi-tos 组件的 v0.2.2 版本开始，需要配置 NodeStageSecretRef 参数，内容与 nodePublishSecretRef 相同。另外，v0.2.2 版本只能兼容 volumeHandle 和 PV Name 的存量 PV；如果直接升级到 v0.2.3 版本，可以兼容所有存量 PV。
      name: data-get-tos-key
      namespace: airflow
    volumeAttributes:
      bucket: eq-data-closedloop-test
      path: /ray-test
      url: https://tos-s3-cn-beijing.ivolces.com
      fuse_pod_cpu_request: "100m" # fuse pod 资源 CPU 请求量，若未配置，默认使用 100m。
      fuse_pod_cpu_limit: "2" # fuse pod 资源 CPU 限制，若未配置，默认使用 6。
      fuse_pod_memory_request: 100Mi # fuse pod 资源内存请求量，若未配置，默认使用 200Mi。
      fuse_pod_memory_limit: "2Gi" # fuse pod 资源内存限制，若未配置，默认使用 6Gi。
      additional_args: -oparallel_count=20 -oallow_other -oumask=000 # s3fs 进程的 options，详细说明可参考 s3fs 帮助文档。从 csi-tos 组件的 v0.2.8 开始会默认添加 -oparallel_count=20。
      dbglevel: err # s3fs 进程的日志等级，默认为 err。
    volumeHandle: ray-pv # PV 的唯一标识符，需要与 PV name 保持一致。volumeHandle 应该唯一对应 <url>:<bucket><path>，避免相同 volumeHandle 对应不同 <url>:<bucket><path>，否则可能导致错误挂载盘。
  persistentVolumeReclaimPolicy: Retain
  volumeMode: Filesystem


---

apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ray-pvc  # PVC 名称。
  namespace: emr-536iaofa563eq7spmvzy-ray
spec:
  accessModes:  # PVC 访问模式。目前仅支持 ReadWriteMany ，表示多点读写。
  - ReadWriteMany
  resources:
    requests:
      storage: 20Gi  # 声明的存储使用量。
  volumeMode: Filesystem # 挂载对象存储的格式，本示例填写 Filesystem，表示文件系统挂载。
  volumeName: ray-pv  # 绑定到该 PVC 的 PV 名称。
