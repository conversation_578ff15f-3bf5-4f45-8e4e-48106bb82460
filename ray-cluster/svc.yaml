apiVersion: v1
kind: Service
metadata:
  name: ray-head-service
  namespace: emr-536iaofa563eq7spmvzy-ray
  labels:
    app: ray
spec:
  type: NodePort
  selector:
    ray.io/node-type: head
  ports:
    - name: gcs
      protocol: TCP
      port: 6379
      targetPort: 6379
      nodePort: 31163
    - name: dashboard
      protocol: TCP
      port: 8265
      targetPort: 8265
      nodePort: 31182
    - name: client
      protocol: TCP
      port: 10001
      targetPort: 10001
      nodePort: 31101
    - name: jupyter
      protocol: TCP
      port: 8888
      targetPort: 8888
      nodePort: 31188
