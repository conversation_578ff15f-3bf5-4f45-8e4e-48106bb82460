import os
import logging
from datetime import datetime
import tos
import ray
import yaml
import logging

def ensure_dir(fp:str) -> bool:
    if os.path.exists(fp):
        return True
    try:
        os.makedirs(fp)
        return True
    except:
        return False

class TosUtil:
    def __init__(self, yaml_config_path:str = '/data/app/config/closedloop.yml'):
        with open(yaml_config_path, 'r') as f:
            data = yaml.safe_load(f)
            access_key = data['tos']['accessKey']
            secret_key = data['tos']['secretKey']
            endpoint = data['tos']['endpoint']
            region = data['tos']['region']
            self.bucket_name = data['tos']['bucketName']
        # 创建客户端
        logging.info(f"创建客户端: {endpoint}, {region}, {self.bucket_name}")
        self.client = tos.TosClientV2(access_key, secret_key, endpoint, region)

    def list_objects(self, prefix=None, max_keys=10):
        '''
        列出存储桶中的对象

        Args:
            prefix: 前缀
            max_keys: 最大数量

        Returns:
            list: 对象列表
        '''
        try:
            # 列出对象
            response = self.client.list_objects_type2(
                self.bucket_name,
                prefix=prefix,
                max_keys=max_keys
            )

            return response.contents
        except Exception as e:
            logging.error(f"列出对象失败: {str(e)}")
            return []

    def read_file_from_tos(self, key):
        '''
        读取存储桶中的对象

        Args:
            key: 对象键
        '''
        try:
            object_stream = self.client.get_object(self.bucket_name, key)
            return object_stream.read()
        except Exception as e:
            logging.error(f"读取对象失败: {str(e)}")
            return None

    def download_only_file_from_tos(self, key, file_name):
        '''
        下载存储桶中的对象

        Args:
            key: 对象键
            file_name: 本地文件名

        Returns:
            object_stream: 对象流
        '''
        try:
            object_stream = self.client.get_object_to_file(self.bucket_name, key, file_name)
            return object_stream
        except Exception as e:
            logging.error(f"获取对象失败: {str(e)}")
            return None

    def download_file_from_tos(self,key,file_name):
        contents = self.list_objects(key,10000)
        if not contents:
            logging.info(f"given tos key: {key} not exists")
            return None
        tos_file_prefix = key
        if key.endswith("/"):
            tos_file_prefix = key[:-1]
        ret = True
        lst = []
        for c in contents:
            if c.key.endswith("/"):
                logging.info(f"tos key {c.key} is dir, not down it")
                continue
            dst_file = f"{file_name}{c.key.split(tos_file_prefix)[-1]}"
            logging.info(f"tos list key={key}, got file {c}, save dst_file={dst_file}")
            ensure_dir(os.path.dirname(dst_file))
            if not self.download_only_file_from_tos(c.key,dst_file):
                logging.error(f"fail to download from tos: {c.key}")
                ret = False
            else:
                lst.append(dst_file)
        logging.info(f"download_file_from_tos finished, input key={key}, got files(len={len(lst)}):{lst}")
        return ret

    def upload_file_to_tos_multi(self, key, file_name):
        '''
        上传存储桶中的对象 - 分包上传

        Args:
            key: 对象键
            file_name: 本地文件名
        '''
        try:
            self.client.upload_part_from_file(self.bucket_name, key, file_name)
        except Exception as e:
            logging.error(f"上传对象失败: {str(e)}")

    def upload_onlyfile_to_tos(self, key, file_name):
        '''
        上传存储桶中的对象

        Args:
            key: 对象键
            file_name: 本地文件名
        '''
        try:
            if os.path.getsize(file_name) > 4 * 1024 * 1024 * 1024:
                self.upload_file_to_tos_multi(key, file_name)
            else:
                self.client.upload_file(self.bucket_name, key, file_name)
        except Exception as e:
            logging.error(f"上传对象失败: {str(e)}")

    def remove_object(self, key):
        '''
        删除存储桶中的对象

        Args:
            key: 对象键
        '''
        try:
            self.client.delete_object(self.bucket_name, key)
        except Exception as e:
            logging.error(f"删除对象失败: {str(e)}")

    def read_file_meta(self, key: str):
        '''
        读取存储桶中的对象元数据

        Args:
            key: 对象键
        '''
        try:
            response = self.client.head_object(self.bucket_name, key)
            return response.meta
        except Exception as e:
            logging.error(f"读取对象元数据失败: {str(e)}")
            return None

    def tos_file_exists(self, key: str):
        '''
        判断存储桶中的对象是否存在

        Args:
            key: 对象键
        '''
        try:
            response = self.client.head_object(self.bucket_name, key)
            return response.status_code == 200
        except Exception as e:
            logging.error(f"对象不存在: {str(e)}")
            return False

    # 析构
    def __del__(self):
        self.client.close()


@ray.remote
class Counter:
    def __init__(self):
        self.count = 0
        self.size = 0

    def increment(self):
        self.count += 1

    def get_count(self):
        return self.count

    def add_size(self, size):
        self.size += size

    def get_size(self):
        return self.size


@ray.remote
def get_tos_keys(tos_config_path: str, prefix: str):
    tos_util = TosUtil(tos_config_path)
    keys = tos_util.list_objects(prefix, max_keys=1000)
    # 假设 ListedObject 有一个 key 属性
    return [key.key for key in keys]

@ray.remote
def download_file(tos_config_path: str, obj_key: str, local_file_path: str):
    tos_util = TosUtil(tos_config_path)
    x = tos_util.download_only_file_from_tos(obj_key, local_file_path)
    # 确保文件下载成功
    if not os.path.exists(local_file_path):
        raise FileNotFoundError(f"Failed to download file: {obj_key}")
    if os.path.exists(local_file_path):
        ray.get(counter.increment.remote())
        size = os.path.getsize(local_file_path)
        # os.remove(local_file_path)
        ray.get(counter.add_size.remote(size))
    return x


if __name__ == "__main__":
    # 配置文件路径
    tos_config_path = "/tmp/closedloop.yaml"

    # 初始化任务管理器并运行任务
    import time
    t1 = time.time()

    ray.init(address="auto")
    counter = Counter.remote()
    prefix = "data/vizbag/2025/06/07/"
    file_keys = ray.get(get_tos_keys.remote(tos_config_path, prefix))
    res = []
    for key in file_keys:
        local_file_path = f"/tmp/data/{os.path.basename(key)}"
        res.append(download_file.remote(tos_config_path, key, local_file_path))
    ray.get(res)
    total_size = ray.get(counter.get_size.remote())
    print(f"Total size: {total_size}")
    print(f"Time: {time.time() - t1}")
    current_count = ray.get(counter.get_count.remote())
    print(f"Current count: {current_count}")
